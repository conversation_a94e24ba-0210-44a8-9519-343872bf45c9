#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门修复 supporting_facilities_image 字段的 URL 编码问题
主要解决 &amp; 等 HTML 实体编码导致的 URL 截断问题
"""

import os
import time
import json
import oss2
import pymysql
import requests
import urllib.parse
import html
import re
from datetime import datetime
from urllib.parse import urljoin, unquote

# 加载OSS配置
with open('oss.json', 'r') as f:
    oss_config = json.load(f)['oss_settings']

# OSS配置
access_key_id = oss_config['access_key_id']
access_key_secret = oss_config['access_key_secret']
endpoint = oss_config['endpoint']
bucket_name = oss_config['bucket_name']
oss_domain = oss_config.get('oss_domain', f'https://{bucket_name}.{endpoint}')

# 数据库配置
DB_CONFIG = {
    'host': '**********',
    'user': 'root',
    'password': 'LianDodmx_03',
    'database': 'erp_integration',
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

# 创建下载目录
DOWNLOAD_DIR = 'temp_supporting_facilities_downloads'
os.makedirs(DOWNLOAD_DIR, exist_ok=True)

# 统计信息
stats = {
    'total_parks': 0,
    'parks_with_issues': 0,
    'successfully_fixed': 0,
    'failed_fixes': 0,
    'skipped': 0
}

def log_message(message, level="INFO"):
    """记录日志信息"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"[{timestamp}] [{level}] {message}")

def connect_to_db():
    """连接到数据库"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        log_message("数据库连接成功")
        return connection
    except Exception as e:
        log_message(f"数据库连接失败: {e}", "ERROR")
        return None

def decode_html_entities(url):
    """处理URL中的编码问题，将 &amp; 删除而不是转换"""
    if not url:
        return url

    try:
        # 先处理下划线开头的编码（如 _amp; -> &amp;）
        url_fixed = url
        underscore_mappings = {
            '_amp;': '&amp;',
            '_lt;': '&lt;',
            '_gt;': '&gt;',
            '_quot;': '&quot;',
            '_#39;': '&#39;',
            '_nbsp;': '&nbsp;'
        }

        for underscore_pattern, html_entity in underscore_mappings.items():
            if underscore_pattern in url_fixed:
                url_fixed = url_fixed.replace(underscore_pattern, html_entity)
                log_message(f"下划线编码转换: {underscore_pattern} -> {html_entity}", "DEBUG")

        # 直接删除 &amp; 而不是转换为 &
        if '&amp;' in url_fixed:
            decoded_url = url_fixed.replace('&amp;', '')
            log_message(f"删除 &amp;: {url_fixed} -> {decoded_url}", "DEBUG")
        else:
            decoded_url = url_fixed

        return decoded_url
    except Exception as e:
        log_message(f"URL处理失败: {url} - {e}", "ERROR")
        return url

def needs_fixing(url):
    """检查 URL 是否包含 &amp; 需要修复"""
    if not url:
        return False

    # 只检查是否包含 &amp;
    if '&amp;' in url:
        log_message(f"发现需要修复的URL（包含 &amp;）: {url}")
        return True

    return False

def get_parks_with_supporting_facilities_issues(connection):
    """获取包含 &amp; 的 supporting_facilities_image 字段的园区"""
    try:
        with connection.cursor() as cursor:
            # 只查询包含 &amp; 的 supporting_facilities_image 字段
            sql = """
            SELECT id, park_name, supporting_facilities_image
            FROM advanced_gallery
            WHERE supporting_facilities_image IS NOT NULL
            AND supporting_facilities_image != ''
            AND supporting_facilities_image LIKE '%&amp;%'
            """
            cursor.execute(sql)
            parks = cursor.fetchall()
            log_message(f"找到 {len(parks)} 个园区的 supporting_facilities_image 字段包含 &amp;")
            return parks
    except Exception as e:
        log_message(f"获取园区信息失败: {e}", "ERROR")
        return []

def download_image(url, save_path):
    """下载图片并保存到指定路径 - 直接使用原始URL下载"""
    if not url:
        return False

    try:
        # 直接使用原始URL进行下载，不做任何处理
        final_url = unquote(url)

        headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36'
        }

        log_message(f"开始下载图片: {final_url}")
        response = requests.get(final_url, timeout=30, headers=headers)

        if response.status_code == 200:
            with open(save_path, 'wb') as f:
                f.write(response.content)
            log_message(f"图片下载成功: {save_path}")
            return True
        else:
            log_message(f"下载失败 (HTTP {response.status_code}): {final_url}", "ERROR")
            return False

    except Exception as e:
        log_message(f"下载出错: {url} - {str(e)}", "ERROR")
        return False

def clean_filename(name):
    """清理文件名，移除非法字符"""
    if not name:
        return "unknown_file"
    
    # 移除文件名中的非法字符
    cleaned = re.sub(r'[\\/*?:"<>|&]', '_', str(name))
    # 移除多余的下划线
    cleaned = re.sub(r'_+', '_', cleaned)
    return cleaned.strip('_')

def extract_filename_from_url(url):
    """从URL中提取文件名，并生成清理后的新文件名"""
    if not url:
        return "unknown_file.jpg"

    try:
        # 从原始URL中提取文件名（用于下载）
        original_filename = os.path.basename(unquote(url).split('?')[0])

        # 生成清理后的文件名（用于上传）
        # 先处理下划线编码
        clean_name = original_filename
        if '_amp;' in clean_name:
            clean_name = clean_name.replace('_amp;', '')

        # 再处理 &amp; 编码
        if '&amp;' in clean_name:
            clean_name = clean_name.replace('&amp;', '')

        # 如果没有扩展名，默认为 .jpg
        if not os.path.splitext(clean_name)[1]:
            clean_name += '.jpg'

        # 进一步清理文件名中的非法字符
        clean_name = clean_filename(clean_name)

        log_message(f"文件名处理: {original_filename} -> {clean_name}")
        return clean_name

    except Exception as e:
        log_message(f"提取文件名失败: {url} - {e}", "ERROR")
        return "unknown_file.jpg"

def upload_to_oss(local_file_path, park_name, original_filename):
    """上传文件到OSS并返回URL"""
    try:
        # 创建Bucket实例
        auth = oss2.Auth(access_key_id, access_key_secret)
        bucket = oss2.Bucket(auth, endpoint, bucket_name)

        # 获取文件扩展名
        file_ext = os.path.splitext(local_file_path)[1]

        # 生成新的文件名（清理特殊字符）
        clean_name = clean_filename(original_filename)
        if not clean_name.endswith(file_ext):
            clean_name = f"{clean_name}{file_ext}"

        # 生成OSS对象名称（使用园区名称作为目录）
        clean_park_name = clean_filename(park_name)
        object_name = f"parks/{clean_park_name}/supporting_facilities/{clean_name}"

        # 上传文件
        log_message(f"开始上传文件到OSS: {object_name}")
        bucket.put_object_from_file(object_name, local_file_path)

        # 生成访问URL
        url = f"https://{bucket_name}.{endpoint}/{object_name}"
        if oss_domain:
            url = urljoin(oss_domain, object_name)

        log_message(f"文件上传成功: {url}")
        return url

    except Exception as e:
        log_message(f"文件上传失败: {e}", "ERROR")
        return None

def backup_original_url(connection, park_id, original_url):
    """备份原始URL到备份表（可选功能）"""
    try:
        with connection.cursor() as cursor:
            # 检查是否存在备份表，如果不存在则创建
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS supporting_facilities_url_backup (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    park_id INT NOT NULL,
                    original_url TEXT,
                    backup_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_park_id (park_id)
                )
            """)

            # 插入备份记录
            cursor.execute(
                "INSERT INTO supporting_facilities_url_backup (park_id, original_url) VALUES (%s, %s)",
                (park_id, original_url)
            )
            connection.commit()
            log_message(f"已备份原始URL: 园区ID {park_id}")
            return True

    except Exception as e:
        log_message(f"备份原始URL失败: {e}", "ERROR")
        return False

def update_supporting_facilities_image(connection, park_id, new_image_url, original_url):
    """更新 supporting_facilities_image 字段"""
    try:
        with connection.cursor() as cursor:
            # 先备份原始URL
            backup_original_url(connection, park_id, original_url)

            # 更新图片URL
            sql = """
                UPDATE advanced_gallery
                SET supporting_facilities_image = %s,
                    upload_time = %s
                WHERE id = %s
            """
            cursor.execute(
                sql,
                (new_image_url, datetime.now().strftime('%Y-%m-%d %H:%M:%S'), park_id)
            )
            connection.commit()
            log_message(f"已更新园区 {park_id} 的 supporting_facilities_image 字段")
            return True

    except Exception as e:
        log_message(f"更新图片URL失败: {e}", "ERROR")
        return False

def process_single_park(connection, park):
    """处理单个园区的 supporting_facilities_image 字段"""
    park_id = park['id']
    park_name = park['park_name']
    original_url = park['supporting_facilities_image']

    log_message(f"\n开始处理园区: {park_name} (ID: {park_id})")
    log_message(f"原始URL: {original_url}")

    # 检查是否需要修复
    if not needs_fixing(original_url):
        log_message(f"园区 {park_name} 的URL不需要修复，跳过")
        stats['skipped'] += 1
        return True

    stats['parks_with_issues'] += 1

    try:
        # 提取文件名
        original_filename = extract_filename_from_url(original_url)
        if not original_filename:
            log_message(f"无法从URL提取文件名，跳过: {original_url}", "ERROR")
            stats['failed_fixes'] += 1
            return False

        # 下载图片
        temp_file_path = os.path.join(DOWNLOAD_DIR, f"{park_id}_{original_filename}")
        if not download_image(original_url, temp_file_path):
            log_message(f"下载图片失败，跳过园区: {park_name}", "ERROR")
            stats['failed_fixes'] += 1
            return False

        # 检查文件是否真的下载成功
        if not os.path.exists(temp_file_path) or os.path.getsize(temp_file_path) == 0:
            log_message(f"下载的文件无效，跳过园区: {park_name}", "ERROR")
            stats['failed_fixes'] += 1
            return False

        # 上传到OSS
        new_image_url = upload_to_oss(temp_file_path, park_name, original_filename)
        if not new_image_url:
            log_message(f"上传图片失败，跳过园区: {park_name}", "ERROR")
            stats['failed_fixes'] += 1
            return False

        # 更新数据库
        if update_supporting_facilities_image(connection, park_id, new_image_url, original_url):
            log_message(f"✅ 成功修复园区 {park_name}: {original_url} -> {new_image_url}")
            stats['successfully_fixed'] += 1
            return True
        else:
            log_message(f"更新数据库失败，园区: {park_name}", "ERROR")
            stats['failed_fixes'] += 1
            return False

    except Exception as e:
        log_message(f"处理园区 {park_name} 时发生错误: {e}", "ERROR")
        stats['failed_fixes'] += 1
        return False

    finally:
        # 清理临时文件
        try:
            if 'temp_file_path' in locals() and os.path.exists(temp_file_path):
                os.remove(temp_file_path)
                log_message(f"已清理临时文件: {temp_file_path}")
        except Exception as e:
            log_message(f"清理临时文件失败: {e}", "WARNING")

def print_statistics():
    """打印统计信息"""
    log_message("\n" + "="*60)
    log_message("修复统计报告")
    log_message("="*60)
    log_message(f"总园区数量: {stats['total_parks']}")
    log_message(f"发现问题的园区: {stats['parks_with_issues']}")
    log_message(f"成功修复: {stats['successfully_fixed']}")
    log_message(f"修复失败: {stats['failed_fixes']}")
    log_message(f"跳过处理: {stats['skipped']}")

    if stats['parks_with_issues'] > 0:
        success_rate = (stats['successfully_fixed'] / stats['parks_with_issues']) * 100
        log_message(f"修复成功率: {success_rate:.1f}%")

    log_message("="*60)

def main():
    """主函数"""
    log_message("开始执行 supporting_facilities_image 字段修复程序")
    log_message(f"下载目录: {DOWNLOAD_DIR}")

    # 连接数据库
    connection = connect_to_db()
    if not connection:
        log_message("无法连接数据库，程序退出", "ERROR")
        return

    try:
        # 获取需要修复的园区信息
        parks = get_parks_with_supporting_facilities_issues(connection)
        stats['total_parks'] = len(parks)

        if not parks:
            log_message("没有发现需要修复的园区，程序结束")
            return

        log_message(f"开始处理 {len(parks)} 个园区...")

        # 处理每个园区
        for i, park in enumerate(parks, 1):
            log_message(f"\n进度: {i}/{len(parks)}")
            process_single_park(connection, park)

            # 每处理5个园区暂停一下，避免对服务器造成过大压力
            if i % 5 == 0:
                log_message("暂停2秒...")
                time.sleep(2)

        # 打印最终统计信息
        print_statistics()

        if stats['successfully_fixed'] > 0:
            log_message(f"\n✅ 程序执行完成！成功修复了 {stats['successfully_fixed']} 个园区的图片链接")
        else:
            log_message("\n⚠️ 程序执行完成，但没有成功修复任何图片链接")

    except KeyboardInterrupt:
        log_message("\n用户中断程序执行", "WARNING")
        print_statistics()

    except Exception as e:
        log_message(f"程序执行失败: {e}", "ERROR")
        print_statistics()

    finally:
        # 关闭数据库连接
        if connection:
            connection.close()
            log_message("数据库连接已关闭")

        # 清理临时目录
        try:
            import shutil
            if os.path.exists(DOWNLOAD_DIR):
                shutil.rmtree(DOWNLOAD_DIR)
                log_message(f"已清理临时目录: {DOWNLOAD_DIR}")
        except Exception as e:
            log_message(f"清理临时目录失败: {e}", "WARNING")

if __name__ == "__main__":
    main()
