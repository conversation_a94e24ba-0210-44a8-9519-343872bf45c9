import os
import json
import oss2
import pymysql
import re
import hashlib
from datetime import datetime
from urllib.parse import urljoin
from pypinyin import lazy_pinyin, Style

# 加载OSS配置
with open('oss.json', 'r') as f:
    oss_config = json.load(f)['oss_settings']

# OSS配置
access_key_id = oss_config['access_key_id']
access_key_secret = oss_config['access_key_secret']
endpoint = oss_config['endpoint']
bucket_name = oss_config['bucket_name']
oss_domain = oss_config.get('oss_domain', f'https://{bucket_name}.{endpoint}')

# 数据库配置（需要替换为实际配置）
DB_CONFIG = {
    'host': '**********',
    'user': 'root',
    'password': 'LianDodmx_03',
    'database': 'erp_integration',
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

# 图片类型与数据库字段映射
IMAGE_TYPE_MAPPING = {
    '1-总平图': 'total_plan_image',
    '2-效果图': 'effect_image',
    '3-户型图': 'floor_plan_image',
    '4-外观图（外立面）': 'facade_image',
    '5-园区进门图（大门）': 'entrance_image',
    '6-园区食堂': 'canteen_image',
    '7-园区内部道路&停车位': 'road_parking_image',
    '8-厂房内部1楼': 'factory_floor1_image',
    '9-厂房内部楼上照片': 'factory_upper_floors_image',
    '10-园区配套（宿舍&超时等）': 'supporting_facilities_image',
    '11-园区内其他您认为需要展示的区域': 'other_areas_image'
}

def parse_directory_name(dir_name):
    """
    解析目录名，提取记录ID和纯净的园区名称
    输入格式: "序号_项目名称" (如: "110_上海奉贤南桥项目（金磊）")
    返回: (record_id, clean_park_name)
    """
    try:
        # 使用正则表达式分离序号和项目名称
        match = re.match(r'^(\d+)_(.+)$', dir_name)
        if match:
            record_id = match.group(1)
            raw_park_name = match.group(2)
            
            # 清理园区名称，移除可能的特殊字符但保持可读性
            # 只移除文件系统不允许的字符，保留中文括号等
            clean_park_name = raw_park_name.replace('_', ' ').strip()
            
            return record_id, clean_park_name
        else:
            # 如果格式不匹配，返回原名称
            return None, dir_name
    except Exception as e:
        print(f"解析目录名失败: {dir_name} - {e}")
        return None, dir_name

def convert_chinese_to_english(text):
    """将中文文本转换为英文标识符"""
    try:
        # 使用拼音转换中文
        pinyin_list = lazy_pinyin(text, style=Style.NORMAL)

        # 连接拼音并清理特殊字符
        pinyin_name = ''.join(pinyin_list)

        # 移除或替换特殊字符，只保留字母数字和下划线点号
        clean_name = re.sub(r'[^a-zA-Z0-9_.]', '_', pinyin_name)

        # 移除连续的下划线
        clean_name = re.sub(r'_+', '_', clean_name)

        # 移除开头和结尾的下划线
        clean_name = clean_name.strip('_')

        return clean_name
    except Exception as e:
        # 如果出错，使用哈希作为备选方案
        try:
            hash_object = hashlib.md5(text.encode('utf-8'))
            return hash_object.hexdigest()[:12]
        except:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            return f"fallback_{timestamp}"

def generate_english_park_name(park_name):
    """将中文园区名称转换为英文标识符"""
    try:
        clean_name = convert_chinese_to_english(park_name)

        # 限制长度并添加前缀
        if len(clean_name) > 50:
            clean_name = clean_name[:50]

        # 如果转换后为空，使用哈希作为备选
        if not clean_name:
            hash_object = hashlib.md5(park_name.encode('utf-8'))
            clean_name = hash_object.hexdigest()[:12]

        english_name = f"park_{clean_name}"

        return english_name
    except Exception as e:
        # 如果出错，使用哈希作为备选方案
        try:
            hash_object = hashlib.md5(park_name.encode('utf-8'))
            hash_hex = hash_object.hexdigest()
            return f"park_{hash_hex[:12]}"
        except:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            return f"park_{timestamp}"

def generate_english_filename(filename):
    """将中文文件名转换为英文文件名"""
    try:
        # 分离文件名和扩展名
        name_part, ext_part = os.path.splitext(filename)

        # 转换文件名部分
        english_name = convert_chinese_to_english(name_part)

        # 限制文件名长度
        if len(english_name) > 100:
            english_name = english_name[:100]

        # 如果转换后为空，使用哈希
        if not english_name:
            hash_object = hashlib.md5(name_part.encode('utf-8'))
            english_name = f"file_{hash_object.hexdigest()[:12]}"

        # 重新组合文件名和扩展名
        return f"{english_name}{ext_part}"
    except Exception as e:
        # 如果出错，生成一个安全的文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_%f')
        ext = os.path.splitext(filename)[1] if '.' in filename else '.jpg'
        return f"file_{timestamp}{ext}"

def get_image_db_field(filename):
    """根据文件名确定对应的数据库字段"""
    for img_type, db_field in IMAGE_TYPE_MAPPING.items():
        if img_type in filename or img_type.split('-')[0] == filename.split('-')[0].strip():
            return db_field
    return None

def connect_to_db():
    """连接到数据库"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        print("数据库连接成功")
        return connection
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return None

def upload_to_oss(local_file_path, park_name, record_id):
    """上传文件到OSS并返回URL"""
    try:
        # 创建Bucket实例
        auth = oss2.Auth(access_key_id, access_key_secret)
        bucket = oss2.Bucket(auth, endpoint, bucket_name)

        # 将中文园区名称转换为英文标识符
        english_park_name = generate_english_park_name(park_name)

        # 将中文文件名转换为英文文件名
        original_filename = os.path.basename(local_file_path)
        english_filename = generate_english_filename(original_filename)

        # 生成OSS对象名称（使用英文园区名称、记录ID和英文文件名）
        object_name = f"parks/{english_park_name}/record_{record_id}/{english_filename}"

        # 上传文件
        bucket.put_object_from_file(object_name, local_file_path)

        # 生成访问URL
        url = f"https://{bucket_name}.{endpoint}/{object_name}"
        if oss_domain:
            url = urljoin(oss_domain, object_name)

        print(f"文件上传成功: {url}")
        print(f"  原始文件名: {original_filename}")
        print(f"  英文文件名: {english_filename}")
        print(f"  中文园区名: {park_name}")
        print(f"  英文标识符: {english_park_name}")
        return url
    except Exception as e:
        print(f"文件上传失败: {e}")
        return None

def insert_park_with_images(connection, park_name, image_data):
    """在数据库中插入新的园区记录，包含所有图片信息"""
    try:
        with connection.cursor() as cursor:
            # 构建插入SQL，包含所有图片字段
            fields = ['park_name', 'upload_time']
            values = [park_name, datetime.now().strftime('%Y-%m-%d %H:%M:%S')]
            placeholders = ['%s', '%s']

            # 添加图片字段
            for field_name, image_url in image_data.items():
                if image_url:  # 只添加有值的字段
                    fields.append(field_name)
                    values.append(image_url)
                    placeholders.append('%s')

            # 构建并执行SQL
            sql = f"INSERT INTO advanced_gallery ({', '.join(fields)}) VALUES ({', '.join(placeholders)})"
            cursor.execute(sql, values)
            connection.commit()
            print(f"已插入新园区记录: {park_name}，包含 {len(image_data)} 张图片")
            return cursor.lastrowid
    except Exception as e:
        print(f"插入园区信息失败: {e}")
        return None

def process_record_directory(connection, record_dir_path):
    """处理单个记录目录"""
    try:
        # 获取目录名并解析
        dir_name = os.path.basename(record_dir_path)
        record_id, park_name = parse_directory_name(dir_name)

        print(f"\n处理记录目录: {dir_name}")
        print(f"园区名称: {park_name}")

        # 获取目录中的所有文件
        files = os.listdir(record_dir_path)

        # 先上传所有图片到OSS，收集图片URL
        image_data = {}
        uploaded_count = 0

        for file_name in files:
            file_path = os.path.join(record_dir_path, file_name)

            # 跳过目录
            if os.path.isdir(file_path):
                continue

            # 获取对应的数据库字段名
            db_field = get_image_db_field(file_name)
            if not db_field:
                print(f"找不到文件对应的数据库字段: {file_name}，跳过")
                continue

            # 上传到OSS（包含记录ID以避免文件覆盖）
            image_url = upload_to_oss(file_path, park_name, record_id)
            if image_url:
                image_data[db_field] = image_url
                uploaded_count += 1

        # 一次性插入园区记录，包含所有图片信息
        if image_data:
            park_id = insert_park_with_images(connection, park_name, image_data)
            if park_id:
                print(f"记录 {dir_name} 处理完成，上传图片数: {uploaded_count}")
                return True
            else:
                print(f"插入园区记录失败: {park_name}")
                return False
        else:
            print(f"记录 {dir_name} 没有有效的图片文件")
            return False

    except Exception as e:
        print(f"处理记录目录失败: {e}")
        return False

def main():
    """主函数"""
    # 连接数据库
    connection = connect_to_db()
    if not connection:
        return

    try:

        # 获取downloaded_images_by_record目录下的所有记录目录
        images_dir = 'downloaded_images_by_record'
        if not os.path.exists(images_dir):
            print(f"目录不存在: {images_dir}")
            return

        record_dirs = [os.path.join(images_dir, d) for d in os.listdir(images_dir)
                      if os.path.isdir(os.path.join(images_dir, d))]

        print(f"共发现 {len(record_dirs)} 个记录目录")

        # 处理每个记录目录
        success_count = 0
        for record_dir in record_dirs:
            if process_record_directory(connection, record_dir):
                success_count += 1

        print(f"\n全部处理完成!")
        print(f"成功处理记录数: {success_count}/{len(record_dirs)}")
    except Exception as e:
        print(f"程序执行失败: {e}")
    finally:
        connection.close()
        print("数据库连接已关闭")

if __name__ == "__main__":
    main()
