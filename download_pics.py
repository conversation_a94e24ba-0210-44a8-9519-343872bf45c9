import pandas as pd
import requests
import os
import re
import time
from concurrent.futures import ThreadPoolExecutor

# 读取Excel文件
excel_path = '不匹配记录_20250627_112314.xlsx'
df = pd.read_excel(excel_path)

# 创建输出目录
output_dir = 'downloaded_images'
os.makedirs(output_dir, exist_ok=True)

# 图片列名模式
image_columns = [col for col in df.columns if col.startswith('图片')]

def clean_filename(name):
    """清理文件名，移除非法字符"""
    if not name or pd.isna(name):
        return "未命名项目"
    # 移除文件名中的非法字符
    return re.sub(r'[\\/*?:"<>|]', '_', str(name))

def download_image(url, save_path, retry=3):
    """下载图片并保存到指定路径"""
    if not url or pd.isna(url):
        return False
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36',
        'Referer': 'https://www.wjx.cn/'  # 假设问卷星域名
    }
    
    for attempt in range(retry):
        try:
            response = requests.get(url, timeout=10, headers=headers)
            if response.status_code == 200:
                with open(save_path, 'wb') as f:
                    f.write(response.content)
                print(f"已下载: {save_path}")
                return True
            else:
                print(f"下载失败 ({response.status_code}): {url}")
        except Exception as e:
            print(f"下载出错 (尝试 {attempt+1}/{retry}): {url} - {str(e)}")
            time.sleep(1)  # 失败后等待1秒再重试
    
    return False

def process_row(row_idx):
    """处理一行数据"""
    row = df.iloc[row_idx]
    
    # 获取项目名称，如果没有则使用序号
    project_name = row.get('项目名称', None)
    if pd.isna(project_name) or not project_name:
        project_name = f"项目_{row.get('序号', row_idx+1)}"
    
    # 清理项目名称作为文件夹名
    project_folder = clean_filename(project_name)
    project_path = os.path.join(output_dir, project_folder)
    os.makedirs(project_path, exist_ok=True)
    
    # 下载该项目的所有图片
    downloaded_count = 0
    for img_col in image_columns:
        # 直接使用Excel中的URL，不做任何处理
        url = row.get(img_col)
        
        if url and not pd.isna(url):
            # 从列名提取图片类型
            img_type = img_col.replace('图片', '')
            
            # 从URL中提取文件扩展名
            if '.' in url.split('/')[-1]:
                # 尝试从URL最后一部分提取扩展名
                file_ext = url.split('/')[-1].split('.')[-1].split('?')[0]
                if len(file_ext) > 5 or not file_ext:  # 如果扩展名不存在或太长，使用jpg
                    file_ext = 'jpg'
            else:
                file_ext = 'jpg'  # 默认使用jpg扩展名
            
            # 从URL或者filename参数中找到原始文件名
            filename_match = re.search(r'filename=([^&]+)', url)
            if filename_match:
                orig_filename = filename_match.group(1)
                # 保留原始文件名，但确保文件类型标识
                filename = f"{img_type}_{orig_filename}"
            else:
                filename = f"{img_type}.{file_ext}"
            
            # 清理文件名中的非法字符
            filename = clean_filename(filename)
            save_path = os.path.join(project_path, filename)
            
            # 下载图片
            if download_image(url, save_path):
                downloaded_count += 1
    
    return project_name, downloaded_count

# 主函数
def main():
    print(f"开始下载 {len(df)} 个项目的图片...")
    
    # 使用多线程下载
    with ThreadPoolExecutor(max_workers=5) as executor:
        results = list(executor.map(process_row, range(len(df))))
    
    # 打印结果统计
    success_count = sum(1 for _, count in results if count > 0)
    total_images = sum(count for _, count in results)
    
    print(f"\n下载完成!")
    print(f"成功下载项目数: {success_count}/{len(df)}")
    print(f"总计下载图片数: {total_images}")
    print(f"图片保存在: {os.path.abspath(output_dir)}")

if __name__ == "__main__":
    main()