#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断 supporting_facilities_image 字段中的URL编码问题
"""

import json
import pymysql
import re
from collections import defaultdict

# 数据库配置
DB_CONFIG = {
    'host': '**********',
    'user': 'root',
    'password': 'LianDodmx_03',
    'database': 'erp_integration',
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

def connect_to_db():
    """连接到数据库"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        print("数据库连接成功")
        return connection
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return None

def analyze_url_patterns(urls):
    """分析URL中的特殊字符模式"""
    patterns = defaultdict(list)
    
    # 定义要检查的模式
    check_patterns = [
        ('&amp;', r'&amp;'),
        ('_amp;', r'_amp;'),
        ('&lt;', r'&lt;'),
        ('&gt;', r'&gt;'),
        ('&quot;', r'&quot;'),
        ('&#39;', r'&#39;'),
        ('&nbsp;', r'&nbsp;'),
        ('_lt;', r'_lt;'),
        ('_gt;', r'_gt;'),
        ('_quot;', r'_quot;'),
        ('_#39;', r'_#39;'),
        ('_nbsp;', r'_nbsp;'),
        ('其他特殊字符', r'[&_][a-zA-Z0-9#]+;'),
    ]
    
    for url_data in urls:
        url = url_data['supporting_facilities_image']
        park_name = url_data['park_name']
        park_id = url_data['id']
        
        for pattern_name, pattern_regex in check_patterns:
            if re.search(pattern_regex, url):
                patterns[pattern_name].append({
                    'park_id': park_id,
                    'park_name': park_name,
                    'url': url
                })
    
    return patterns

def main():
    """主函数"""
    print("开始诊断 supporting_facilities_image 字段...")
    
    # 连接数据库
    connection = connect_to_db()
    if not connection:
        return
    
    try:
        with connection.cursor() as cursor:
            # 获取所有非空的 supporting_facilities_image 字段
            sql = """
            SELECT id, park_name, supporting_facilities_image 
            FROM advanced_gallery 
            WHERE supporting_facilities_image IS NOT NULL 
            AND supporting_facilities_image != ''
            """
            cursor.execute(sql)
            all_urls = cursor.fetchall()
            
            print(f"总共找到 {len(all_urls)} 个园区有 supporting_facilities_image 数据")
            
            if not all_urls:
                print("没有找到任何数据")
                return
            
            # 分析URL模式
            patterns = analyze_url_patterns(all_urls)
            
            print("\n" + "="*80)
            print("URL 模式分析结果")
            print("="*80)
            
            if not patterns:
                print("没有发现任何特殊字符模式")
                print("\n前10个URL示例：")
                for i, url_data in enumerate(all_urls[:10]):
                    print(f"{i+1}. 园区: {url_data['park_name']}")
                    print(f"   URL: {url_data['supporting_facilities_image']}")
                    print()
            else:
                for pattern_name, matches in patterns.items():
                    if matches:
                        print(f"\n发现 {pattern_name} 模式: {len(matches)} 个")
                        print("-" * 40)
                        for match in matches[:5]:  # 只显示前5个
                            print(f"园区ID: {match['park_id']}")
                            print(f"园区名: {match['park_name']}")
                            print(f"URL: {match['url']}")
                            print()
                        if len(matches) > 5:
                            print(f"... 还有 {len(matches) - 5} 个类似的")
                            print()
            
            print("="*80)
            
    except Exception as e:
        print(f"诊断过程中发生错误: {e}")
    
    finally:
        connection.close()
        print("数据库连接已关闭")

if __name__ == "__main__":
    main()
