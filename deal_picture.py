#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
园区图片质检系统
使用阿里云千锤百炼模型 Qwen2.5-VL-32B-Instruct 对advanced_gallery中每个园区的所有图片进行质检
将违规原因、园区名称、违规的图片写入Excel中
"""

import os
import asyncio
import pymysql
import pandas as pd
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from openai import AsyncOpenAI
import logging
import time
from typing import List, Dict, Tuple, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('image_quality_check.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 阿里云千问模型API配置
DASHSCOPE_API_KEY = "sk-61e66c39babd417da86f9f2e6b580492"
os.environ["DASHSCOPE_API_KEY"] = DASHSCOPE_API_KEY

# 数据库配置
DB_CONFIG = {
    'host': '**********',
    'user': 'root',
    'password': 'LianDodmx_03',
    'database': 'erp_integration',
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

# 图片字段映射
IMAGE_FIELDS = {
    'effect_image': '效果图',
    'floor_plan_image': '户型图',
    'facade_image': '外观图(外立面)',
    'entrance_image': '园区进门图(大门)',
    'canteen_image': '园区食堂',
    'road_parking_image': '园区内部道路&停车位',
    'factory_floor1_image': '厂房内部1楼',
    'factory_upper_floors_image': '厂房内部楼上照片',
    'supporting_facilities_image': '园区配套(宿舍&超市等)',
    'other_areas_image': '园区内其他区域'
}

# 质检提示词
QUALITY_CHECK_PROMPT = """#角色定义：你是一名非常善于在58同城\安居客等房产发布网站上进行网络营销的厂房园区的招商顾问，主要工作是参考58对于上传的房源图片的规范要求，对图片是否违规进行判断。

58平台图片违规判定规则如下：
1、包括但不限于地图、路线图、规划图、纯文字介绍、宣传单页、自拍照、突出物品摆设、等与房源无关或无法体现房屋格局的照片。
2、发布的图片存在盗图，包括但不仅限于含有58、赶集、安居客LOGO，半截经纪公司水印、使用裁剪截图、拍屏图等。
3、图片中有明显后添加的字幕，包括不限于汉字、数字、英文字母等各种类型的标记。

#输出要求：输出是否违规的判定结论以及判定理由。

#输出格式：
是否违规：是/否advanced_gallery
判定理由：XXXX（参考判定规则输出）"""

# 处理配置
CONCURRENT_LIMIT = 10  # 并发处理数量限制
MAX_RETRIES = 3  # 最大重试次数
REQUEST_TIMEOUT = 30  # 请求超时时间（秒）


class ImageQualityInspector:
    """图片质检器"""

    def __init__(self):
        """初始化图片质检器"""
        self.async_client = AsyncOpenAI(
            api_key=DASHSCOPE_API_KEY,
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
        )
        self.connection = None
        self.results = []

    def connect_to_db(self) -> bool:
        """连接到数据库"""
        try:
            self.connection = pymysql.connect(**DB_CONFIG)
            logger.info("✅ 数据库连接成功")
            return True
        except Exception as e:
            logger.error(f"❌ 数据库连接失败: {e}")
            return False

    def close_db_connection(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            logger.info("数据库连接已关闭")

    def get_all_park_images(self) -> List[Dict]:
        """从数据库获取所有园区的图片数据"""
        if not self.connection:
            logger.error("数据库未连接")
            return []

        try:
            with self.connection.cursor() as cursor:
                # 构建查询SQL，获取所有园区的图片字段
                image_fields_str = ', '.join(IMAGE_FIELDS.keys())
                sql = f"""
                    SELECT id, park_name, {image_fields_str}
                    FROM advanced_gallery
                    WHERE park_name IS NOT NULL
                    AND park_name != ''
                    ORDER BY id
                """

                cursor.execute(sql)
                parks_data = cursor.fetchall()

                # 将数据转换为图片列表格式
                image_list = []
                for park in parks_data:
                    park_id = park['id']
                    park_name = park['park_name']

                    # 遍历每个图片字段
                    for field_name, field_desc in IMAGE_FIELDS.items():
                        image_url = park.get(field_name)

                        # 跳过空的图片URL
                        if image_url and image_url.strip() and image_url.lower() != 'null':
                            image_list.append({
                                'park_id': park_id,
                                'park_name': park_name,
                                'image_type': field_desc,
                                'image_field': field_name,
                                'image_url': image_url.strip(),
                                'status': '待检查'
                            })

                logger.info(f"📊 从数据库获取到 {len(parks_data)} 个园区，共 {len(image_list)} 张图片")
                return image_list

        except Exception as e:
            logger.error(f"❌ 获取园区图片数据失败: {e}")
            return []

    async def check_image_quality(self, image_data: Dict) -> Dict:
        """检查单张图片的质量"""
        park_name = image_data['park_name']
        image_type = image_data['image_type']
        image_url = image_data['image_url']

        logger.info(f"🔍 检查图片: {park_name} - {image_type}")

        for attempt in range(MAX_RETRIES):
            try:
                # 调用千问模型进行图片质检
                response = await self.async_client.chat.completions.create(
                    model="qwen2.5-vl-32b-instruct",
                    messages=[
                        {
                            "role": "user",
                            "content": [
                                {
                                    "type": "text",
                                    "text": QUALITY_CHECK_PROMPT
                                },
                                {
                                    "type": "image_url",
                                    "image_url": {
                                        "url": image_url
                                    }
                                }
                            ]
                        }
                    ],
                    timeout=REQUEST_TIMEOUT
                )

                # 解析响应
                result_text = response.choices[0].message.content

                # 解析是否违规和违规原因
                is_violation, violation_reason = self._parse_quality_result(result_text)

                # 构建结果
                result = {
                    **image_data,
                    'is_violation': is_violation,
                    'violation_reason': violation_reason,
                    'check_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'status': '检查完成',
                    'api_response': result_text
                }

                # 详细输出AI的判断过程
                logger.info(f"✅ 检查完成: {park_name} - {image_type} - {'违规' if is_violation else '合规'}")
                logger.info(f"🤖 AI完整响应: {result_text}")
                logger.info(f"📝 解析结果: 违规={is_violation}, 原因={violation_reason}")
                return result

            except Exception as e:
                logger.warning(f"⚠️ 检查失败 (尝试 {attempt + 1}/{MAX_RETRIES}): {park_name} - {image_type} - {str(e)}")
                if attempt < MAX_RETRIES - 1:
                    await asyncio.sleep(2 ** attempt)  # 指数退避
                else:
                    # 最后一次尝试失败，返回错误结果
                    result = {
                        **image_data,
                        'is_violation': '检查失败',
                        'violation_reason': f'API调用失败: {str(e)}',
                        'check_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                        'status': '检查失败',
                        'api_response': ''
                    }
                    logger.error(f"❌ 检查失败: {park_name} - {image_type} - {str(e)}")
                    return result

    def _parse_quality_result(self, result_text: str) -> Tuple[str, str]:
        """解析质检结果"""
        try:
            is_violation = '未知'
            violation_reason = '解析失败'

            # 方法1: 查找标准格式
            lines = result_text.strip().split('\n')
            for line in lines:
                line = line.strip()
                if line.startswith('是否违规：') or line.startswith('是否违规:'):
                    violation_status = line.split('：')[-1].split(':')[-1].strip()
                    is_violation = '是' if '是' in violation_status else '否'
                elif line.startswith('判定理由：') or line.startswith('判定理由:'):
                    violation_reason = line.split('：')[-1].split(':')[-1].strip()

            # 方法2: 如果标准格式解析失败，尝试更灵活的解析
            if is_violation == '未知':
                # 查找"是否违规：**是**"或"是否违规：**否**"这样的格式
                import re
                violation_pattern = r'是否违规[：:]\s*\*?\*?([是否])\*?\*?'
                violation_match = re.search(violation_pattern, result_text)
                if violation_match:
                    is_violation = violation_match.group(1)

                # 查找判定理由部分
                reason_pattern = r'判定理由[：:]?\s*([^#\n]*(?:\n(?!#)[^#\n]*)*)'
                reason_match = re.search(reason_pattern, result_text, re.MULTILINE)
                if reason_match:
                    violation_reason = reason_match.group(1).strip()
                elif '判定理由' in result_text:
                    # 如果找到"判定理由"关键词，提取后面的内容
                    reason_start = result_text.find('判定理由')
                    if reason_start != -1:
                        reason_text = result_text[reason_start:].split('\n')[0]
                        if '：' in reason_text or ':' in reason_text:
                            violation_reason = reason_text.split('：')[-1].split(':')[-1].strip()

            # 方法3: 如果还是解析失败，从整体内容推断
            if is_violation == '未知':
                # 查找结论部分
                if '是否违规：**是**' in result_text or '是否违规: **是**' in result_text:
                    is_violation = '是'
                elif '是否违规：**否**' in result_text or '是否违规: **否**' in result_text:
                    is_violation = '否'
                elif '违规' in result_text and ('是' in result_text or '存在' in result_text):
                    is_violation = '是'
                elif '不违规' in result_text or '合规' in result_text:
                    is_violation = '否'

                # 如果确定了违规状态，尝试提取原因
                if is_violation != '未知' and violation_reason == '解析失败':
                    # 提取主要违规原因
                    if '第3条' in result_text or '第三项' in result_text:
                        violation_reason = '图片中有明显后添加的字幕或标记'
                    elif '第1条' in result_text or '第一项' in result_text:
                        violation_reason = '图片与房源无关或无法体现房屋格局'
                    elif '第2条' in result_text or '第二项' in result_text:
                        violation_reason = '图片存在盗图或含有其他平台水印'
                    elif '文字' in result_text and '标注' in result_text:
                        violation_reason = '图片含有后添加的文字标注'
                    else:
                        # 尝试提取关键句子作为原因
                        sentences = result_text.split('。')
                        for sentence in sentences:
                            if '违规' in sentence and len(sentence) < 100:
                                violation_reason = sentence.strip()
                                break

            # 如果violation_reason仍然是"解析失败"，使用AI的完整响应作为原因
            if violation_reason == '解析失败':
                violation_reason = f"AI判断详情请查看完整响应"

            logger.info(f"🔍 解析结果: 违规状态='{is_violation}', 违规原因='{violation_reason[:50]}...'")
            return is_violation, violation_reason

        except Exception as e:
            logger.warning(f"解析质检结果失败: {e}")
            return '解析失败', f'结果解析错误: {str(e)}'

    async def process_images_batch(self, image_list: List[Dict]) -> List[Dict]:
        """并发处理图片质检（10个并发）"""
        total_images = len(image_list)
        logger.info(f"🚀 开始并发处理 {total_images} 张图片，并发限制: {CONCURRENT_LIMIT}")

        # 创建信号量控制并发数量
        semaphore = asyncio.Semaphore(CONCURRENT_LIMIT)

        # 创建进度跟踪
        completed_count = 0
        results_lock = asyncio.Lock()
        all_results = []

        async def process_single_image_with_semaphore(image_data: Dict) -> Dict:
            """使用信号量控制的单图片处理"""
            nonlocal completed_count

            async with semaphore:
                try:
                    result = await self.check_image_quality(image_data)

                    # 线程安全地更新进度
                    async with results_lock:
                        completed_count += 1
                        all_results.append(result)

                        # 实时进度报告
                        success_count = sum(1 for r in all_results if r['status'] == '检查完成')
                        failed_count = completed_count - success_count

                        if completed_count % 5 == 0 or completed_count == total_images:
                            logger.info(f"📊 进度更新: {completed_count}/{total_images} "
                                      f"(成功: {success_count}, 失败: {failed_count})")

                    return result

                except Exception as e:
                    # 处理异常情况
                    error_result = {
                        **image_data,
                        'is_violation': '检查失败',
                        'violation_reason': f'处理异常: {str(e)}',
                        'check_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                        'status': '检查失败',
                        'api_response': ''
                    }

                    async with results_lock:
                        completed_count += 1
                        all_results.append(error_result)

                    logger.error(f"❌ 处理异常: {image_data['park_name']} - {image_data['image_type']} - {str(e)}")
                    return error_result

        # 创建所有任务
        tasks = [process_single_image_with_semaphore(image_data) for image_data in image_list]

        try:
            # 并发执行所有任务，但受信号量限制
            await asyncio.gather(*tasks, return_exceptions=False)

        except Exception as e:
            logger.error(f"❌ 并发处理过程中发生错误: {e}")

        logger.info(f"🎉 并发处理完成，共处理 {len(all_results)} 张图片")
        return all_results

    def save_to_excel(self, results: List[Dict], filename: Optional[str] = None) -> str:
        """将结果保存到Excel文件"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'image_quality_report_{timestamp}.xlsx'

        try:
            # 准备Excel数据
            excel_data = []
            for result in results:
                excel_data.append({
                    '园区ID': result['park_id'],
                    '园区名称': result['park_name'],
                    '图片类型': result['image_type'],
                    '图片URL': result['image_url'],
                    '是否违规': result['is_violation'],
                    '违规原因': result['violation_reason'],
                    'AI完整响应': result['api_response'],  # 添加AI原始响应
                    '检查时间': result['check_time'],
                    '处理状态': result['status']
                })

            # 创建DataFrame
            df = pd.DataFrame(excel_data)

            # 保存到Excel
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # 主要结果表
                df.to_excel(writer, sheet_name='图片质检结果', index=False)

                # 统计汇总表
                summary_data = self._generate_summary_stats(results)
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='统计汇总', index=False)

                # 违规图片汇总表
                violation_results = [r for r in results if r['is_violation'] == '是']
                if violation_results:
                    violation_data = []
                    for result in violation_results:
                        violation_data.append({
                            '园区名称': result['park_name'],
                            '图片类型': result['image_type'],
                            '违规原因': result['violation_reason'],
                            'AI完整判断过程': result['api_response'],  # 添加完整AI响应
                            '图片URL': result['image_url']
                        })
                    violation_df = pd.DataFrame(violation_data)
                    violation_df.to_excel(writer, sheet_name='违规图片汇总', index=False)

            logger.info(f"📄 Excel报告已保存: {filename}")
            return filename

        except Exception as e:
            logger.error(f"❌ 保存Excel文件失败: {e}")
            return ""

    def _generate_summary_stats(self, results: List[Dict]) -> List[Dict]:
        """生成统计汇总数据"""
        total_images = len(results)
        violation_count = sum(1 for r in results if r['is_violation'] == '是')
        compliant_count = sum(1 for r in results if r['is_violation'] == '否')
        failed_count = sum(1 for r in results if r['status'] == '检查失败')

        # 按园区统计
        park_stats = {}
        for result in results:
            park_name = result['park_name']
            if park_name not in park_stats:
                park_stats[park_name] = {'total': 0, 'violation': 0, 'compliant': 0, 'failed': 0}

            park_stats[park_name]['total'] += 1
            if result['is_violation'] == '是':
                park_stats[park_name]['violation'] += 1
            elif result['is_violation'] == '否':
                park_stats[park_name]['compliant'] += 1
            else:
                park_stats[park_name]['failed'] += 1

        # 按图片类型统计
        type_stats = {}
        for result in results:
            img_type = result['image_type']
            if img_type not in type_stats:
                type_stats[img_type] = {'total': 0, 'violation': 0, 'compliant': 0, 'failed': 0}

            type_stats[img_type]['total'] += 1
            if result['is_violation'] == '是':
                type_stats[img_type]['violation'] += 1
            elif result['is_violation'] == '否':
                type_stats[img_type]['compliant'] += 1
            else:
                type_stats[img_type]['failed'] += 1

        # 构建汇总数据
        summary_data = [
            {'统计项目': '总图片数', '数量': total_images, '百分比': '100.0%'},
            {'统计项目': '违规图片', '数量': violation_count, '百分比': f'{violation_count/total_images*100:.1f}%' if total_images > 0 else '0%'},
            {'统计项目': '合规图片', '数量': compliant_count, '百分比': f'{compliant_count/total_images*100:.1f}%' if total_images > 0 else '0%'},
            {'统计项目': '检查失败', '数量': failed_count, '百分比': f'{failed_count/total_images*100:.1f}%' if total_images > 0 else '0%'},
            {'统计项目': '', '数量': '', '百分比': ''},
            {'统计项目': '园区数量', '数量': len(park_stats), '百分比': ''},
            {'统计项目': '图片类型数', '数量': len(type_stats), '百分比': ''}
        ]

        return summary_data

    async def run_quality_check(self, park_id_filter: Optional[List[int]] = None) -> str:
        """运行完整的图片质检流程"""
        logger.info("🚀 开始园区图片质检...")

        # 连接数据库
        if not self.connect_to_db():
            return ""

        try:
            # 获取所有园区图片数据
            image_list = self.get_all_park_images()
            if not image_list:
                logger.warning("⚠️ 未找到需要检查的图片")
                return ""

            # 如果指定了园区ID过滤器，则过滤图片列表
            if park_id_filter:
                image_list = [img for img in image_list if img['park_id'] in park_id_filter]
                logger.info(f"🔍 已过滤到指定园区，剩余 {len(image_list)} 张图片")

            if not image_list:
                logger.warning("⚠️ 过滤后无图片需要检查")
                return ""

            # 并发处理图片质检
            results = await self.process_images_batch(image_list)

            # 保存结果到Excel
            excel_filename = self.save_to_excel(results)

            # 打印最终统计
            self._print_final_statistics(results)

            return excel_filename

        except Exception as e:
            logger.error(f"❌ 质检流程执行失败: {e}")
            return ""
        finally:
            self.close_db_connection()

    def _print_final_statistics(self, results: List[Dict]):
        """打印最终统计信息"""
        total_images = len(results)
        violation_count = sum(1 for r in results if r['is_violation'] == '是')
        compliant_count = sum(1 for r in results if r['is_violation'] == '否')
        failed_count = sum(1 for r in results if r['status'] == '检查失败')

        logger.info("\n" + "="*60)
        logger.info("📊 图片质检完成统计")
        logger.info("="*60)
        logger.info(f"总图片数: {total_images}")
        logger.info(f"违规图片: {violation_count} ({violation_count/total_images*100:.1f}%)" if total_images > 0 else "违规图片: 0")
        logger.info(f"合规图片: {compliant_count} ({compliant_count/total_images*100:.1f}%)" if total_images > 0 else "合规图片: 0")
        logger.info(f"检查失败: {failed_count} ({failed_count/total_images*100:.1f}%)" if total_images > 0 else "检查失败: 0")

        # 显示违规园区
        if violation_count > 0:
            violation_parks = set()
            for result in results:
                if result['is_violation'] == '是':
                    violation_parks.add(result['park_name'])

            logger.info(f"\n违规园区数量: {len(violation_parks)}")
            for park in sorted(violation_parks):
                park_violations = [r for r in results if r['park_name'] == park and r['is_violation'] == '是']
                logger.info(f"  - {park}: {len(park_violations)} 张违规图片")

        logger.info("="*60)


async def main():
    """主函数"""
    import argparse

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='园区图片质检系统')
    parser.add_argument('--park-ids', type=str, help='指定园区ID列表，用逗号分隔，例如: 1,2,3')
    parser.add_argument('--output', type=str, help='指定输出Excel文件名')

    args = parser.parse_args()

    # 解析园区ID过滤器
    park_id_filter = None
    if args.park_ids:
        try:
            park_id_filter = [int(pid.strip()) for pid in args.park_ids.split(',')]
            logger.info(f"🎯 指定检查园区ID: {park_id_filter}")
        except ValueError:
            logger.error("❌ 园区ID格式错误，请使用逗号分隔的数字")
            return

    # 创建质检器并运行
    inspector = ImageQualityInspector()

    start_time = time.time()
    excel_filename = await inspector.run_quality_check(park_id_filter)
    end_time = time.time()

    if excel_filename:
        logger.info(f"✅ 质检完成！耗时: {end_time - start_time:.2f} 秒")
        logger.info(f"📄 报告文件: {excel_filename}")
    else:
        logger.error("❌ 质检失败")


if __name__ == "__main__":
    # 运行异步主函数
    asyncio.run(main())