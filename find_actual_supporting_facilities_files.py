#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查找OSS中实际存在的园区配套设施图片文件
"""

import json
import oss2
import pymysql
from datetime import datetime

# 加载OSS配置
with open('oss.json', 'r') as f:
    oss_config = json.load(f)['oss_settings']

# OSS配置
access_key_id = oss_config['access_key_id']
access_key_secret = oss_config['access_key_secret']
endpoint = oss_config['endpoint']
bucket_name = oss_config['bucket_name']

# 数据库配置
DB_CONFIG = {
    'host': '**********',
    'user': 'root',
    'password': 'LianDodmx_03',
    'database': 'erp_integration',
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

def connect_to_db():
    """连接到数据库"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        print("数据库连接成功")
        return connection
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return None

def get_problematic_parks():
    """获取有问题的园区信息"""
    connection = connect_to_db()
    if not connection:
        return []
    
    try:
        with connection.cursor() as cursor:
            sql = """
            SELECT id, park_name, supporting_facilities_image 
            FROM advanced_gallery 
            WHERE supporting_facilities_image LIKE '%_amp;%'
            """
            cursor.execute(sql)
            parks = cursor.fetchall()
            return parks
    except Exception as e:
        print(f"获取园区信息失败: {e}")
        return []
    finally:
        connection.close()

def search_oss_files(park_name):
    """在OSS中搜索园区相关的配套设施图片"""
    try:
        # 创建Bucket实例
        auth = oss2.Auth(access_key_id, access_key_secret)
        bucket = oss2.Bucket(auth, endpoint, bucket_name)
        
        # 搜索可能的文件路径
        possible_prefixes = [
            f"parks/{park_name}/",
            f"parks/{park_name.replace('（', '(').replace('）', ')')}/",
        ]
        
        found_files = []
        
        for prefix in possible_prefixes:
            try:
                # 列出该前缀下的所有文件
                for obj in oss2.ObjectIterator(bucket, prefix=prefix):
                    filename = obj.key.lower()
                    # 查找可能的配套设施图片
                    if any(keyword in filename for keyword in ['配套', '宿舍', '超市', 'supporting', 'facilities']):
                        file_url = f"https://{bucket_name}.{endpoint}/{obj.key}"
                        found_files.append({
                            'key': obj.key,
                            'url': file_url,
                            'size': obj.size,
                            'last_modified': obj.last_modified
                        })
            except Exception as e:
                print(f"搜索前缀 {prefix} 时出错: {e}")
                continue
        
        return found_files
        
    except Exception as e:
        print(f"搜索OSS文件失败: {e}")
        return []

def update_supporting_facilities_url(park_id, new_url):
    """更新园区的supporting_facilities_image字段"""
    connection = connect_to_db()
    if not connection:
        return False
    
    try:
        with connection.cursor() as cursor:
            sql = """
            UPDATE advanced_gallery 
            SET supporting_facilities_image = %s, 
                upload_time = %s 
            WHERE id = %s
            """
            cursor.execute(sql, (new_url, datetime.now().strftime('%Y-%m-%d %H:%M:%S'), park_id))
            connection.commit()
            print(f"✅ 已更新园区 {park_id} 的URL为: {new_url}")
            return True
    except Exception as e:
        print(f"更新数据库失败: {e}")
        return False
    finally:
        connection.close()

def main():
    """主函数"""
    print("开始查找实际存在的园区配套设施图片...")
    
    # 获取有问题的园区
    parks = get_problematic_parks()
    print(f"找到 {len(parks)} 个需要修复的园区")
    
    fixed_count = 0
    not_found_count = 0
    
    for park in parks:
        park_id = park['id']
        park_name = park['park_name']
        current_url = park['supporting_facilities_image']
        
        print(f"\n处理园区: {park_name} (ID: {park_id})")
        print(f"当前URL: {current_url}")
        
        # 在OSS中搜索实际文件
        found_files = search_oss_files(park_name)
        
        if found_files:
            print(f"找到 {len(found_files)} 个可能的文件:")
            for i, file_info in enumerate(found_files):
                print(f"  {i+1}. {file_info['key']}")
                print(f"     URL: {file_info['url']}")
                print(f"     大小: {file_info['size']} bytes")
                print()
            
            # 选择最合适的文件（通常是第一个）
            best_file = found_files[0]
            
            # 更新数据库
            if update_supporting_facilities_url(park_id, best_file['url']):
                fixed_count += 1
            
        else:
            print("❌ 未找到任何相关文件")
            not_found_count += 1
    
    print(f"\n" + "="*60)
    print("修复完成统计:")
    print(f"成功修复: {fixed_count}")
    print(f"未找到文件: {not_found_count}")
    print("="*60)

if __name__ == "__main__":
    main()
