import os
import json
import oss2
import pymysql
from datetime import datetime
from urllib.parse import urljoin

# 加载OSS配置
with open('oss.json', 'r') as f:
    oss_config = json.load(f)['oss_settings']

# OSS配置
access_key_id = oss_config['access_key_id']
access_key_secret = oss_config['access_key_secret']
endpoint = oss_config['endpoint']
bucket_name = oss_config['bucket_name']
oss_domain = oss_config.get('oss_domain', f'https://{bucket_name}.{endpoint}')

# 数据库配置（需要替换为实际配置）
DB_CONFIG = {
    'host': '**********',
    'user': 'root',
    'password': 'LianDodmx_03',
    'database': 'erp_integration',
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

# 图片类型与数据库字段映射
IMAGE_TYPE_MAPPING = {
    '1-总平图': 'total_plan_image',
    '2-效果图': 'effect_image',
    '3-户型图': 'floor_plan_image',
    '4-外观图（外立面）': 'facade_image',
    '5-园区进门图（大门）': 'entrance_image',
    '6-园区食堂': 'canteen_image',
    '7-园区内部道路&停车位': 'road_parking_image',
    '8-厂房内部1楼': 'factory_floor1_image',
    '9-厂房内部楼上照片': 'factory_upper_floors_image',
    '10-园区配套（宿舍&超时等）': 'supporting_facilities_image',
    '11-园区内其他您认为需要展示的区域': 'other_areas_image'
}

def get_image_db_field(filename):
    """根据文件名确定对应的数据库字段"""
    for img_type, db_field in IMAGE_TYPE_MAPPING.items():
        if img_type in filename or img_type.split('-')[0] == filename.split('-')[0].strip():
            return db_field
    return None

def connect_to_db():
    """连接到数据库"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        print("数据库连接成功")
        return connection
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return None

def upload_to_oss(local_file_path, park_name):
    """上传文件到OSS并返回URL"""
    try:
        # 创建Bucket实例
        auth = oss2.Auth(access_key_id, access_key_secret)
        bucket = oss2.Bucket(auth, endpoint, bucket_name)
        
        # 生成OSS对象名称（使用园区名称作为目录）
        file_name = os.path.basename(local_file_path)
        object_name = f"parks/{park_name}/{file_name}"
        
        # 上传文件
        bucket.put_object_from_file(object_name, local_file_path)
        
        # 生成访问URL
        url = f"https://{bucket_name}.{endpoint}/{object_name}"
        if oss_domain:
            url = urljoin(oss_domain, object_name)
            
        print(f"文件上传成功: {url}")
        return url
    except Exception as e:
        print(f"文件上传失败: {e}")
        return None

def insert_or_update_park(connection, park_name):
    """在数据库中插入或更新园区信息，返回园区ID"""
    try:
        with connection.cursor() as cursor:
            # 查询园区是否已存在
            cursor.execute("SELECT id FROM advanced_gallery WHERE park_name = %s", (park_name,))
            result = cursor.fetchone()
            
            if result:
                return result['id']
            else:
                # 插入新园区
                cursor.execute(
                    "INSERT INTO advanced_gallery (park_name, upload_time) VALUES (%s, %s)",
                    (park_name, datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
                )
                connection.commit()
                return cursor.lastrowid
    except Exception as e:
        print(f"插入或更新园区信息失败: {e}")
        return None

def update_park_image(connection, park_id, field_name, image_url):
    """更新园区图片URL"""
    try:
        with connection.cursor() as cursor:
            # 更新图片URL
            sql = f"UPDATE advanced_gallery SET {field_name} = %s, upload_time = %s WHERE id = %s"
            cursor.execute(
                sql,
                (image_url, datetime.now().strftime('%Y-%m-%d %H:%M:%S'), park_id)
            )
            connection.commit()
            print(f"已更新 {field_name} 字段")
            return True
    except Exception as e:
        print(f"更新图片URL失败: {e}")
        return False

def process_park_directory(connection, park_dir_path):
    """处理单个园区目录"""
    try:
        # 获取园区名称
        park_name = os.path.basename(park_dir_path)
        print(f"\n处理园区: {park_name}")
        
        # 获取或创建园区ID
        park_id = insert_or_update_park(connection, park_name)
        if not park_id:
            print(f"无法获取园区ID，跳过: {park_name}")
            return False
        
        # 获取目录中的所有文件
        files = os.listdir(park_dir_path)
        
        # 上传每个文件并更新数据库
        for file_name in files:
            file_path = os.path.join(park_dir_path, file_name)
            
            # 跳过目录
            if os.path.isdir(file_path):
                continue
                
            # 获取对应的数据库字段名
            db_field = get_image_db_field(file_name)
            if not db_field:
                print(f"找不到文件对应的数据库字段: {file_name}，跳过")
                continue
            
            # 上传到OSS
            image_url = upload_to_oss(file_path, park_name)
            if not image_url:
                continue
                
            # 更新数据库
            update_park_image(connection, park_id, db_field, image_url)
        
        print(f"园区 {park_name} 处理完成")
        return True
    except Exception as e:
        print(f"处理园区目录失败: {e}")
        return False

def main():
    """主函数"""
    # 连接数据库
    connection = connect_to_db()
    if not connection:
        return
    
    try:
        # 获取downloaded_images目录下的所有园区目录
        images_dir = 'downloaded_images'
        park_dirs = [os.path.join(images_dir, d) for d in os.listdir(images_dir) 
                    if os.path.isdir(os.path.join(images_dir, d))]
        
        print(f"共发现 {len(park_dirs)} 个园区目录")
        
        # 处理每个园区目录
        success_count = 0
        for park_dir in park_dirs:
            if process_park_directory(connection, park_dir):
                success_count += 1
        
        print(f"\n全部处理完成!")
        print(f"成功处理园区数: {success_count}/{len(park_dirs)}")
    except Exception as e:
        print(f"程序执行失败: {e}")
    finally:
        connection.close()
        print("数据库连接已关闭")

if __name__ == "__main__":
    main() 